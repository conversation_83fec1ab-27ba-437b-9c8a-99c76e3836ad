#!/usr/bin/env python3
"""
测试baostock数据同步功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sync_kline_by_concepts_baostock import ConceptBasedKlineSyncBaostock
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_baostock_connection():
    """测试baostock连接"""
    print("🧪 测试baostock连接...")
    
    syncer = ConceptBasedKlineSyncBaostock()
    
    # 测试登录
    if syncer.login_baostock():
        print("✅ baostock连接成功")
        
        # 测试获取单个股票数据
        test_code = "000001"  # 平安银行
        print(f"🧪 测试获取股票 {test_code} 的数据...")
        
        start_date = "2024-01-01"
        df = syncer.get_kline_data_from_baostock(test_code, start_date)
        
        if df is not None and not df.empty:
            print(f"✅ 成功获取股票 {test_code} 数据，共 {len(df)} 条记录")
            print("📊 数据样例:")
            print(df.head())
            print("\n📊 数据列:")
            print(df.columns.tolist())
        else:
            print(f"❌ 未能获取股票 {test_code} 数据")
            
        return True
    else:
        print("❌ baostock连接失败")
        return False

def test_stock_code_conversion():
    """测试股票代码转换"""
    print("\n🧪 测试股票代码转换...")
    
    syncer = ConceptBasedKlineSyncBaostock()
    
    test_codes = [
        "000001",    # 深圳
        "000002",    # 深圳
        "300001",    # 创业板
        "600000",    # 上海
        "600036",    # 上海
        "688001",    # 科创板
        "SZ000001",  # 带前缀
        "SH600000",  # 带前缀
    ]
    
    for code in test_codes:
        bs_code = syncer.convert_stock_code_to_baostock(code)
        print(f"  {code} -> {bs_code}")

def test_analyze_concepts():
    """测试概念分析功能"""
    print("\n🧪 测试概念分析功能...")
    
    syncer = ConceptBasedKlineSyncBaostock()
    
    try:
        hot_concepts = syncer.analyze_hot_concepts()
        if hot_concepts:
            print(f"✅ 成功分析出 {len(hot_concepts)} 个热门概念")
            return True
        else:
            print("❌ 未能分析出热门概念")
            return False
    except Exception as e:
        print(f"❌ 概念分析失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试baostock数据同步功能")
    print("=" * 60)
    
    # 测试1: baostock连接
    success1 = test_baostock_connection()
    
    # 测试2: 股票代码转换
    test_stock_code_conversion()
    
    # 测试3: 概念分析
    success3 = test_analyze_concepts()
    
    print("\n" + "=" * 60)
    print("🎯 测试结果总结:")
    print(f"  baostock连接: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"  概念分析: {'✅ 成功' if success3 else '❌ 失败'}")
    
    if success1 and success3:
        print("\n🎉 所有测试通过！可以使用 sync_kline_by_concepts_baostock.py 进行数据同步")
        print("\n💡 使用方法:")
        print("  python3 sync_kline_by_concepts_baostock.py --analyze-only  # 只分析概念")
        print("  python3 sync_kline_by_concepts_baostock.py                 # 完整同步")
    else:
        print("\n⚠️ 部分测试失败，请检查配置")

if __name__ == '__main__':
    main()
