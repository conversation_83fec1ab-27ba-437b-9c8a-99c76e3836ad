#!/usr/bin/env python3
"""
基于热门概念同步股票日K数据 - 使用baostock数据源
"""

import pandas as pd
import numpy as np
import logging
import time
import random
import json
import os
import baostock as bs
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text, Column, String, Integer, Float, Date, DateTime, Text
from sqlalchemy.orm import sessionmaker, declarative_base
from collections import Counter
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('concept_kline_sync_baostock.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
engine = create_engine(Config.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()


# 股票K线数据模型（简化版，只包含基本字段）
class StockKlineData(Base):
    __tablename__ = 'stock_kline_data'

    id = Column(Integer, primary_key=True, autoincrement=True)
    stock_code = Column(String(20), nullable=False)
    trade_date = Column(Date, nullable=False)
    open_price = Column(Float)
    high_price = Column(Float)
    low_price = Column(Float)
    close_price = Column(Float)
    volume = Column(Float)
    amount = Column(Float)
    turnover_rate = Column(Float)


# 同步进度记录模型
class SyncProgress(Base):
    __tablename__ = 'sync_progress'

    id = Column(Integer, primary_key=True, autoincrement=True)
    sync_date = Column(Date, nullable=False)  # 同步日期
    total_stocks = Column(Integer, nullable=False)  # 总股票数
    completed_stocks = Column(Integer, default=0)  # 已完成股票数
    failed_stocks = Column(Integer, default=0)  # 失败股票数
    completed_codes = Column(Text, default='')  # 已完成的股票代码（JSON格式）
    failed_codes = Column(Text, default='')  # 失败的股票代码（JSON格式）
    status = Column(String(20), default='running')  # 状态：running, completed, failed
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class ConceptBasedKlineSyncBaostock:
    """基于概念的K线数据同步器 - 使用baostock数据源"""

    def __init__(self, top_count=18, blocked_concepts=None, lookback_months=6):
        self.session = SessionLocal()

        # 配置参数
        self.TOP_CONCEPTS_COUNT = top_count  # 取前N个热门概念
        self.BLOCKED_CONCEPTS = blocked_concepts or [  # 屏蔽的概念
            '年报增长', '并购重组', '基础建设', '零售', '房地产', '地产链', '股权转让', '汽车零部件', '金融概念',
            '汽车类', '人工智能', '数字经济'
        ]
        self.LOOKBACK_MONTHS = lookback_months  # 回看几个月的数据

        # 请求配置
        self.MIN_DELAY = 1  # 最小延迟时间（秒）- baostock不需要太长延迟
        self.MAX_DELAY = 3  # 最大延迟时间（秒）
        self.RETRY_DELAY = 10  # 失败重试延迟（秒）
        self.MAX_RETRIES = 3  # 最大重试次数

        # 进度管理
        self.progress_file = 'sync_progress_baostock.json'  # 本地进度文件
        self.today = datetime.now().date()

        # baostock登录状态
        self.bs_logged_in = False

        # 创建表
        Base.metadata.create_all(engine)

    def __del__(self):
        if hasattr(self, 'session'):
            self.session.close()
        # 登出baostock
        if self.bs_logged_in:
            bs.logout()

    def login_baostock(self):
        """登录baostock系统"""
        try:
            lg = bs.login()
            if lg.error_code == '0':
                self.bs_logged_in = True
                logger.info("✅ baostock登录成功")
                return True
            else:
                logger.error(f"❌ baostock登录失败: {lg.error_msg}")
                return False
        except Exception as e:
            logger.error(f"❌ baostock登录异常: {str(e)}")
            return False

    def convert_stock_code_to_baostock(self, stock_code):
        """将股票代码转换为baostock格式"""
        try:
            # 移除可能的前缀和后缀
            code = stock_code.replace('SZ', '').replace('SH', '').replace('.', '')
            
            # 确保是6位数字
            if len(code) == 6 and code.isdigit():
                # 根据代码判断市场
                if code.startswith('00') or code.startswith('30'):
                    return f"sz.{code}"  # 深圳市场
                elif code.startswith('60') or code.startswith('68'):
                    return f"sh.{code}"  # 上海市场
                else:
                    # 默认深圳市场
                    return f"sz.{code}"
            else:
                logger.warning(f"无效的股票代码格式: {stock_code}")
                return None
        except Exception as e:
            logger.error(f"转换股票代码失败 {stock_code}: {str(e)}")
            return None

    def get_kline_data_from_baostock(self, stock_code, start_date):
        """从baostock获取K线数据"""
        try:
            # 转换股票代码格式
            bs_code = self.convert_stock_code_to_baostock(stock_code)
            if not bs_code:
                return None

            # 计算结束日期（今天）
            end_date = datetime.now().strftime('%Y-%m-%d')

            logger.info(f"正在获取股票 {stock_code} ({bs_code}) 的K线数据，时间范围: {start_date} 到 {end_date}")

            # 获取历史K线数据
            rs = bs.query_history_k_data_plus(
                bs_code,
                "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
                start_date=start_date,
                end_date=end_date,
                frequency="d",
                adjustflag="3"  # 不复权
            )

            if rs.error_code != '0':
                logger.error(f"获取股票 {stock_code} 数据失败: {rs.error_msg}")
                return None

            # 处理数据
            data_list = []
            while (rs.error_code == '0') & rs.next():
                data_list.append(rs.get_row_data())

            if not data_list:
                logger.warning(f"股票 {stock_code} 未获取到数据")
                return None

            # 转换为DataFrame
            result = pd.DataFrame(data_list, columns=rs.fields)

            # 数据格式转换，使其与原来的adata格式兼容
            if not result.empty:
                # 重命名列以匹配原格式
                result = result.rename(columns={
                    'date': 'trade_date',
                    'open': 'open',
                    'high': 'high', 
                    'low': 'low',
                    'close': 'close',
                    'volume': 'volume',
                    'amount': 'amount',
                    'turn': 'turnover_ratio'
                })

                # 数据类型转换
                numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'turnover_ratio']
                for col in numeric_columns:
                    if col in result.columns:
                        result[col] = pd.to_numeric(result[col], errors='coerce')

                # 过滤掉停牌的数据
                if 'tradestatus' in result.columns:
                    result = result[result['tradestatus'] == '1']  # 1表示正常交易

                logger.info(f"✅ 股票 {stock_code} 获取到 {len(result)} 条数据")
                return result
            else:
                logger.warning(f"股票 {stock_code} 数据为空")
                return None

        except Exception as e:
            logger.error(f"从baostock获取股票 {stock_code} 数据失败: {str(e)}")
            return None

    def check_daily_reset(self):
        """检查是否需要每日重置"""
        try:
            # 检查今天是否已经有同步记录
            today_progress = self.session.query(SyncProgress).filter(
                SyncProgress.sync_date == self.today
            ).first()

            if not today_progress:
                # 新的一天，清空K线数据表
                logger.info(f"🗓️ 新的一天 {self.today}，清空K线数据表...")
                deleted_count = self.session.query(StockKlineData).delete()
                self.session.commit()
                logger.info(f"✅ 清空了 {deleted_count} 条K线数据")
                return True
            else:
                logger.info(f"📅 今天 {self.today} 已有同步记录，继续之前的进度")
                return False

        except Exception as e:
            logger.error(f"检查每日重置失败: {str(e)}")
            return False

    def get_or_create_progress(self, total_stocks):
        """获取或创建同步进度记录"""
        try:
            progress = self.session.query(SyncProgress).filter(
                SyncProgress.sync_date == self.today
            ).first()

            if not progress:
                # 创建新的进度记录
                progress = SyncProgress(
                    sync_date=self.today,
                    total_stocks=total_stocks,
                    completed_stocks=0,
                    failed_stocks=0,
                    completed_codes='[]',
                    failed_codes='[]',
                    status='running'
                )
                self.session.add(progress)
                self.session.commit()
                logger.info(f"📊 创建新的同步进度记录，总股票数: {total_stocks}")
            else:
                logger.info(f"📊 继续之前的同步进度: {progress.completed_stocks}/{progress.total_stocks}")

            return progress

        except Exception as e:
            logger.error(f"获取同步进度失败: {str(e)}")
            return None

    def update_progress(self, progress, stock_code, success=True):
        """更新同步进度"""
        try:
            if success:
                progress.completed_stocks += 1
                completed_codes = json.loads(progress.completed_codes or '[]')
                completed_codes.append(stock_code)
                progress.completed_codes = json.dumps(completed_codes)
            else:
                progress.failed_stocks += 1
                failed_codes = json.loads(progress.failed_codes or '[]')
                failed_codes.append(stock_code)
                progress.failed_codes = json.dumps(failed_codes)

            # 检查是否完成
            if progress.completed_stocks + progress.failed_stocks >= progress.total_stocks:
                progress.status = 'completed'
                logger.info(f"🎉 同步完成！成功: {progress.completed_stocks}, 失败: {progress.failed_stocks}")

            progress.updated_at = datetime.now()
            self.session.commit()

        except Exception as e:
            logger.error(f"更新同步进度失败: {str(e)}")

    def get_remaining_stocks(self, all_stock_codes):
        """获取剩余需要同步的股票"""
        try:
            progress = self.session.query(SyncProgress).filter(
                SyncProgress.sync_date == self.today
            ).first()

            if not progress:
                return all_stock_codes

            # 获取已完成的股票代码
            completed_codes = json.loads(progress.completed_codes or '[]')
            failed_codes = json.loads(progress.failed_codes or '[]')
            processed_codes = set(completed_codes + failed_codes)

            # 返回未处理的股票
            remaining_codes = [code for code in all_stock_codes if code not in processed_codes]

            logger.info(f"📊 剩余需要同步的股票: {len(remaining_codes)}/{len(all_stock_codes)}")
            return remaining_codes

        except Exception as e:
            logger.error(f"获取剩余股票失败: {str(e)}")
            return all_stock_codes

    def analyze_hot_concepts(self):
        """分析热门概念"""
        try:
            logger.info("📊 开始分析热门概念...")

            # 获取所有sector数据
            query = text("""
                SELECT sector
                FROM stock_daily_data
                WHERE sector IS NOT NULL AND sector != ''
            """)

            result = self.session.execute(query)
            sectors = [row[0] for row in result.fetchall()]

            logger.info(f"获取到 {len(sectors)} 条sector数据")

            # 分析概念
            all_concepts = []
            for sector in sectors:
                if sector:
                    # 按"、"分隔概念
                    concepts = [concept.strip() for concept in sector.split('、') if concept.strip()]
                    all_concepts.extend(concepts)

            logger.info(f"总共提取到 {len(all_concepts)} 个概念")

            # 统计概念出现次数
            concept_counter = Counter(all_concepts)

            # 过滤屏蔽的概念
            filtered_concepts = {k: v for k, v in concept_counter.items()
                                 if k not in self.BLOCKED_CONCEPTS}

            # 获取前N个热门概念
            top_concepts = dict(Counter(filtered_concepts).most_common(self.TOP_CONCEPTS_COUNT))

            # 显示结果
            print("\n" + "=" * 80)
            print(f"🔥 前{self.TOP_CONCEPTS_COUNT}个热门概念（已屏蔽: {', '.join(self.BLOCKED_CONCEPTS)}）")
            print("=" * 80)

            for i, (concept, count) in enumerate(top_concepts.items(), 1):
                print(f"  {i:2d}. {concept:<15} - {count:4d} 次")

            logger.info(f"筛选出 {len(top_concepts)} 个热门概念")
            return list(top_concepts.keys())

        except Exception as e:
            logger.error(f"分析热门概念失败: {str(e)}")
            return []

    def get_stocks_by_concepts(self, concepts):
        """根据概念获取相关股票"""
        try:
            logger.info(f"📊 根据概念获取相关股票...")

            # 构建SQL查询条件
            concept_conditions = []
            for concept in concepts:
                concept_conditions.append(f"sector LIKE '%{concept}%'")

            where_clause = " OR ".join(concept_conditions)

            query = text(f"""
                SELECT DISTINCT stock_code, stock_name, sector
                FROM stock_daily_data
                WHERE {where_clause}
                ORDER BY stock_code
            """)

            result = self.session.execute(query)
            stocks = result.fetchall()

            logger.info(f"找到 {len(stocks)} 个相关股票")

            # 显示部分股票信息
            print(f"\n📋 找到 {len(stocks)} 个相关股票，显示前10个：")
            for i, stock in enumerate(stocks[:10], 1):
                print(f"  {i:2d}. {stock[0]} - {stock[1]} ({stock[2]})")

            if len(stocks) > 10:
                print(f"  ... 还有 {len(stocks) - 10} 个股票")

            return [stock[0] for stock in stocks]

        except Exception as e:
            logger.error(f"获取相关股票失败: {str(e)}")
            return []

    def save_kline_data_to_db(self, df, stock_code):
        """保存K线数据到数据库"""
        if df is None or df.empty:
            logger.warning(f"股票 {stock_code} 数据为空，跳过保存")
            return False

        try:
            # 检查是否今天已经同步过这个股票
            today = datetime.now().date()
            existing_count = self.session.query(StockKlineData).filter(
                StockKlineData.stock_code == stock_code
            ).count()

            if existing_count > 0:
                logger.info(f"股票 {stock_code} 今日已同步过，跳过重复处理")
                return True

            # 准备批量插入的数据
            kline_records = []

            for idx, row in df.iterrows():
                try:
                    # 处理日期格式
                    if isinstance(row['trade_date'], str):
                        trade_date = datetime.strptime(row['trade_date'], '%Y-%m-%d').date()
                    else:
                        trade_date = row['trade_date']

                    kline_data = StockKlineData(
                        stock_code=stock_code,
                        trade_date=trade_date,
                        open_price=float(row['open']) if pd.notna(row['open']) else None,
                        high_price=float(row['high']) if pd.notna(row['high']) else None,
                        low_price=float(row['low']) if pd.notna(row['low']) else None,
                        close_price=float(row['close']) if pd.notna(row['close']) else None,
                        volume=float(row['volume']) if pd.notna(row['volume']) else None,
                        amount=float(row['amount']) if 'amount' in row and pd.notna(row['amount']) else None,
                        turnover_rate=float(row['turnover_ratio']) if 'turnover_ratio' in row and pd.notna(
                            row['turnover_ratio']) else None,
                    )
                    kline_records.append(kline_data)

                except Exception as e:
                    logger.warning(f"处理股票 {stock_code} 第 {idx} 行数据失败: {str(e)}")
                    continue

            if kline_records:
                # 批量插入新数据（已经检查过不存在重复）
                self.session.add_all(kline_records)
                self.session.commit()

                logger.info(f"✅ 股票 {stock_code} 保存成功，共 {len(kline_records)} 条数据")
                return True
            else:
                logger.warning(f"股票 {stock_code} 没有有效数据可保存")
                return False

        except Exception as e:
            self.session.rollback()
            logger.error(f"保存股票 {stock_code} K线数据失败: {str(e)}")
            return False

    def sync_kline_data(self, stock_codes):
        """同步K线数据（支持断点续传）"""
        try:
            # 检查每日重置
            self.check_daily_reset()

            # 获取剩余需要同步的股票
            remaining_codes = self.get_remaining_stocks(stock_codes)

            if not remaining_codes:
                logger.info("✅ 所有股票已同步完成！")
                return

            # 获取或创建进度记录
            progress = self.get_or_create_progress(len(stock_codes))
            if not progress:
                logger.error("❌ 无法创建进度记录，退出同步")
                return

            # 登录baostock
            if not self.login_baostock():
                logger.error("❌ baostock登录失败，退出同步")
                return

            # 计算起始日期
            start_date = (datetime.now() - timedelta(days=self.LOOKBACK_MONTHS * 30)).strftime('%Y-%m-%d')

            logger.info(f"🚀 开始同步K线数据，起始日期: {start_date}")
            logger.info(f"📊 总股票数: {len(stock_codes)}, 剩余: {len(remaining_codes)}")
            logger.info(f"📊 已完成: {progress.completed_stocks}, 已失败: {progress.failed_stocks}")

            current_success = 0
            current_failed = 0

            for i, stock_code in enumerate(remaining_codes, 1):
                # 计算当前总进度
                current_total_processed = progress.completed_stocks + progress.failed_stocks + i

                retry_count = 0
                success = False

                while retry_count < self.MAX_RETRIES and not success:
                    try:
                        if retry_count > 0:
                            logger.info(f"[{current_total_processed}/{len(stock_codes)}] 重试第 {retry_count} 次获取股票 {stock_code} 的K线数据...")
                        else:
                            logger.info(f"[{current_total_processed}/{len(stock_codes)}] 正在获取股票 {stock_code} 的K线数据...")

                        # 获取K线数据
                        res_df = self.get_kline_data_from_baostock(stock_code, start_date)

                        if res_df is not None and not res_df.empty:
                            # 保存数据到数据库
                            if self.save_kline_data_to_db(res_df, stock_code):
                                logger.info(f"✅ 股票 {stock_code} 获取并保存成功，数据量: {len(res_df)}")
                                current_success += 1
                                success = True
                                # 更新进度
                                self.update_progress(progress, stock_code, success=True)
                            else:
                                logger.warning(f"⚠️ 股票 {stock_code} 获取成功但保存失败")
                                retry_count += 1
                        else:
                            logger.warning(f"⚠️ 股票 {stock_code} 未获取到数据")
                            retry_count += 1

                        if success:
                            # 成功后随机延迟
                            delay = random.uniform(self.MIN_DELAY, self.MAX_DELAY)
                            time.sleep(delay)

                    except Exception as e:
                        retry_count += 1
                        logger.error(f"❌ 股票 {stock_code} 获取失败 (重试 {retry_count}/{self.MAX_RETRIES}): {str(e)}")

                        if retry_count < self.MAX_RETRIES:
                            # 重试前延迟
                            time.sleep(self.RETRY_DELAY)

                # 如果所有重试都失败了
                if not success:
                    current_failed += 1
                    # 更新进度
                    self.update_progress(progress, stock_code, success=False)
                    logger.error(f"❌ 股票 {stock_code} 经过 {self.MAX_RETRIES} 次重试后仍然失败")

                # 每处理10个股票显示一次进度
                if i % 10 == 0:
                    total_completed = progress.completed_stocks + current_success
                    total_failed = progress.failed_stocks + current_failed
                    logger.info(
                        f"📊 当前进度: {total_completed + total_failed}/{len(stock_codes)}, 成功: {total_completed}, 失败: {total_failed}")

            total_success = progress.completed_stocks + current_success
            total_failed = progress.failed_stocks + current_failed
            logger.info(f"🎉 本次同步完成！本次成功: {current_success}, 本次失败: {current_failed}")
            logger.info(f"📊 总体进度: 成功: {total_success}, 失败: {total_failed}, 总计: {len(stock_codes)}")

        except Exception as e:
            logger.error(f"同步K线数据失败: {str(e)}")
        finally:
            # 确保登出baostock
            if self.bs_logged_in:
                bs.logout()
                self.bs_logged_in = False
                logger.info("✅ baostock已登出")

    def run(self):
        """运行完整流程"""
        try:
            print("🚀 基于热门概念的K线数据同步器启动 (baostock数据源)")
            print(f"配置: 取前{self.TOP_CONCEPTS_COUNT}个概念, 屏蔽概念: {', '.join(self.BLOCKED_CONCEPTS)}")

            # 1. 分析热门概念
            hot_concepts = self.analyze_hot_concepts()

            if not hot_concepts:
                logger.error("❌ 未获取到热门概念，程序退出")
                return

            # 2. 获取相关股票
            stock_codes = self.get_stocks_by_concepts(hot_concepts)

            if not stock_codes:
                logger.error("❌ 未获取到相关股票，程序退出")
                return

            # 3. 确认是否继续
            print(f"\n📊 将同步 {len(stock_codes)} 个股票的K线数据")
            # confirm = input("是否继续？(y/N): ").strip().lower()
            #
            # if confirm != 'y':
            #     print("❌ 用户取消操作")
            #     return

            # 4. 同步K线数据
            self.sync_kline_data(stock_codes)

        except KeyboardInterrupt:
            logger.info("⚠️ 用户中断程序")
        except Exception as e:
            logger.error(f"程序执行异常: {str(e)}")
        finally:
            # 确保登出baostock
            if self.bs_logged_in:
                bs.logout()
                self.bs_logged_in = False


def main():
    """主函数"""
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--analyze-only':
            # 只分析概念，不同步数据
            print("🔍 概念分析模式（不同步数据）")
            syncer = ConceptBasedKlineSyncBaostock()
            hot_concepts = syncer.analyze_hot_concepts()
            if hot_concepts:
                stock_codes = syncer.get_stocks_by_concepts(hot_concepts)
                print(f"\n📊 基于这些概念将同步 {len(stock_codes)} 个股票的K线数据")
            return
        elif sys.argv[1] == '--help':
            print("用法:")
            print("  python3 sync_kline_by_concepts_baostock.py              # 完整运行")
            print("  python3 sync_kline_by_concepts_baostock.py --analyze-only  # 只分析概念")
            print("  python3 sync_kline_by_concepts_baostock.py --help          # 显示帮助")
            return

    # 完整运行
    syncer = ConceptBasedKlineSyncBaostock()
    syncer.run()


if __name__ == '__main__':
    main()
